import type { FileInfo } from "./fileSelector";
import type { UploadStrategy, UploadStrategyAnalysis } from "./newUploadTypes";
import { SmartPacker } from "./smartPacker";

/**
 * 上传策略分析器
 * 根据文件数量、大小、类型等因素智能选择最佳上传策略
 */
export class UploadStrategyAnalyzer {
  private static instance: UploadStrategyAnalyzer;

  // 策略阈值配置
  private static readonly INDIVIDUAL_THRESHOLD = 10;
  private static readonly BATCH_THRESHOLD = 50;
  private static readonly SMART_PACK_THRESHOLD = 50;
  private static readonly LARGE_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly TOTAL_SIZE_THRESHOLD = 1024 * 1024 * 1024; // 1GB

  public static getInstance(): UploadStrategyAnalyzer {
    if (!UploadStrategyAnalyzer.instance) {
      UploadStrategyAnalyzer.instance = new UploadStrategyAnalyzer();
    }
    return UploadStrategyAnalyzer.instance;
  }

  /**
   * 分析文件列表并推荐最佳上传策略
   */
  public analyzeUploadStrategy(files: FileInfo[]): UploadStrategyAnalysis {
    const fileCount = files.length;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const averageFileSize = totalSize / fileCount;
    const largeFileCount = files.filter(file => file.size > UploadStrategyAnalyzer.LARGE_FILE_SIZE).length;
    const hasDirectories = files.some(file => file.isDirectory);

    // 分析结果
    let strategy: UploadStrategy;
    let reason: string;
    let shouldPack = false;

    // 策略决策逻辑
    if (fileCount <= UploadStrategyAnalyzer.INDIVIDUAL_THRESHOLD) {
      // 少量文件：单独上传
      strategy = 'individual';
      reason = `文件数量较少（${fileCount}个），适合单独上传`;
    } else if (fileCount > UploadStrategyAnalyzer.SMART_PACK_THRESHOLD) {
      // 大量文件：智能打包
      strategy = 'smart-pack';
      shouldPack = true;
      reason = `文件数量较多（${fileCount}个），使用压缩包上传可提高效率`;
    } else if (totalSize > UploadStrategyAnalyzer.TOTAL_SIZE_THRESHOLD) {
      // 总大小较大：根据文件特征决策
      if (largeFileCount > fileCount * 0.5) {
        // 大文件较多：批量上传
        strategy = 'batch';
        reason = `包含较多大文件，批量上传可更好地管理进度`;
      } else {
        // 小文件较多：智能打包
        strategy = 'smart-pack';
        shouldPack = true;
        reason = `包含大量小文件，压缩后上传可减少网络开销`;
      }
    } else if (hasDirectories) {
      // 包含目录：批量上传保持结构
      strategy = 'batch';
      reason = `包含文件夹，批量上传可保持目录结构`;
    } else {
      // 中等数量文件：批量上传
      strategy = 'batch';
      reason = `文件数量适中（${fileCount}个），批量上传效率较高`;
    }

    // 估算上传时间（简化计算）
    const estimatedTime = this.estimateUploadTime(totalSize, strategy, fileCount);

    return {
      strategy,
      shouldPack,
      fileCount,
      totalSize,
      estimatedTime,
      reason,
    };
  }

  /**
   * 估算上传时间（秒）
   */
  private estimateUploadTime(totalSize: number, strategy: UploadStrategy, fileCount: number): number {
    // 基础上传速度（字节/秒）- 假设平均网速
    const baseUploadSpeed = 5 * 1024 * 1024; // 5MB/s

    // 根据策略调整速度
    let adjustedSpeed = baseUploadSpeed;
    let overhead = 0;

    switch (strategy) {
      case 'individual':
        // 单独上传：每个文件都有连接开销
        adjustedSpeed = baseUploadSpeed * 0.8; // 效率降低20%
        overhead = fileCount * 2; // 每个文件2秒开销
        break;
      
      case 'batch':
        // 批量上传：连接复用，效率较高
        adjustedSpeed = baseUploadSpeed * 0.95; // 效率降低5%
        overhead = Math.ceil(fileCount / 10) * 3; // 每10个文件3秒开销
        break;
      
      case 'smart-pack':
        // 智能打包：需要压缩时间，但上传效率高
        const compressionTime = this.estimateCompressionTime(totalSize, fileCount);
        adjustedSpeed = baseUploadSpeed * 1.1; // 效率提升10%
        overhead = compressionTime + 5; // 压缩时间 + 5秒上传开销
        break;
    }

    const uploadTime = totalSize / adjustedSpeed;
    return Math.ceil(uploadTime + overhead);
  }

  /**
   * 估算压缩时间（秒）
   */
  private estimateCompressionTime(totalSize: number, fileCount: number): number {
    // 压缩速度假设：50MB/s（快速压缩模式）
    const compressionSpeed = 50 * 1024 * 1024;
    
    // 基础压缩时间
    const baseTime = totalSize / compressionSpeed;
    
    // 文件数量影响（文件越多，压缩开销越大）
    const fileCountFactor = Math.log10(fileCount + 1) * 2;
    
    return Math.ceil(baseTime + fileCountFactor);
  }

  /**
   * 检查是否应该使用智能打包
   */
  public shouldUsePacking(files: FileInfo[]): boolean {
    return SmartPacker.shouldPack(files);
  }

  /**
   * 获取策略建议文本
   */
  public getStrategyRecommendation(analysis: UploadStrategyAnalysis): string {
    const { strategy, fileCount, totalSize, estimatedTime } = analysis;
    const sizeText = this.formatFileSize(totalSize);
    const timeText = this.formatTime(estimatedTime);

    let recommendation = `推荐使用${this.getStrategyName(strategy)}策略上传 ${fileCount} 个文件（${sizeText}）。`;
    recommendation += `\n预计用时：${timeText}`;
    recommendation += `\n原因：${analysis.reason}`;

    return recommendation;
  }

  /**
   * 获取策略名称
   */
  private getStrategyName(strategy: UploadStrategy): string {
    switch (strategy) {
      case 'individual':
        return '单独上传';
      case 'batch':
        return '批量上传';
      case 'smart-pack':
        return '智能打包';
      default:
        return '未知策略';
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化时间
   */
  private formatTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
    }
  }

  /**
   * 比较两个策略的优劣
   */
  public compareStrategies(files: FileInfo[], strategy1: UploadStrategy, strategy2: UploadStrategy): {
    better: UploadStrategy;
    reason: string;
    timeDifference: number;
  } {
    const analysis1 = { ...this.analyzeUploadStrategy(files), strategy: strategy1 };
    const analysis2 = { ...this.analyzeUploadStrategy(files), strategy: strategy2 };

    analysis1.estimatedTime = this.estimateUploadTime(analysis1.totalSize, strategy1, analysis1.fileCount);
    analysis2.estimatedTime = this.estimateUploadTime(analysis2.totalSize, strategy2, analysis2.fileCount);

    const timeDifference = Math.abs(analysis1.estimatedTime - analysis2.estimatedTime);
    
    if (analysis1.estimatedTime < analysis2.estimatedTime) {
      return {
        better: strategy1,
        reason: `${this.getStrategyName(strategy1)}比${this.getStrategyName(strategy2)}快约${this.formatTime(timeDifference)}`,
        timeDifference,
      };
    } else if (analysis2.estimatedTime < analysis1.estimatedTime) {
      return {
        better: strategy2,
        reason: `${this.getStrategyName(strategy2)}比${this.getStrategyName(strategy1)}快约${this.formatTime(timeDifference)}`,
        timeDifference,
      };
    } else {
      return {
        better: strategy1,
        reason: '两种策略用时相近',
        timeDifference: 0,
      };
    }
  }
}
