/**
 * 新上传架构的类型定义
 * 用于统一管理文件选择、智能打包、拖拽等功能的类型
 */

import type { FileInfo, FileSelectOptions, FileSelectResult } from "./fileSelector";
import type { PackingTask, PackingOptions, PackingStatus } from "./smartPacker";
import type { ApiResponse } from "./types";

// ==================== 拖拽相关类型 ====================

/**
 * 拖拽文件信息
 */
export interface DragDropFileInfo {
  path: string;
  name: string;
  size: number;
  type: string;
  isDirectory: boolean;
}

/**
 * 拖拽事件数据
 */
export interface DragDropEventData {
  files: DragDropFileInfo[];
  totalSize: number;
  totalCount: number;
  hasDirectories: boolean;
}

// ==================== 上传策略相关类型 ====================

/**
 * 上传策略类型
 */
export type UploadStrategy = 'individual' | 'batch' | 'smart-pack';

/**
 * 上传策略分析结果
 */
export interface UploadStrategyAnalysis {
  strategy: UploadStrategy;
  shouldPack: boolean;
  fileCount: number;
  totalSize: number;
  estimatedTime: number;
  reason: string;
}

// ==================== 智能上传相关类型 ====================

/**
 * 智能上传请求
 */
export interface SmartUploadRequest {
  files: FileInfo[];
  metadata?: Record<string, string>;
  options?: {
    forceStrategy?: UploadStrategy;
    packingOptions?: PackingOptions;
  };
}

/**
 * 智能上传响应
 */
export interface SmartUploadResponse {
  strategy: UploadStrategy;
  taskIds: string[];
  packingTaskId?: string;
  analysis: UploadStrategyAnalysis;
}

// ==================== 事件相关类型 ====================

/**
 * 文件选择事件
 */
export interface FileSelectEvent {
  type: 'file-select-start' | 'file-select-progress' | 'file-select-complete' | 'file-select-error';
  data?: {
    progress?: number;
    currentFile?: string;
    result?: FileSelectResult;
    error?: string;
  };
}

/**
 * 打包进度事件
 */
export interface PackingProgressEvent {
  taskId: string;
  progress: number;
  processedSize: number;
  totalSize: number;
  currentFile?: string;
  speed?: number;
  remainingTime?: number;
}

/**
 * 打包状态变更事件
 */
export interface PackingStatusEvent {
  taskId: string;
  status: PackingStatus;
  error?: string;
  task?: PackingTask;
}

// ==================== IPC 接口定义 ====================

/**
 * 新上传架构的 IPC 接口
 */
export interface NewUploadIpcApi {
  // 文件选择
  selectFiles: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;
  selectDirectories: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;
  processFilePaths: (filePaths: string[]) => Promise<ApiResponse<FileSelectResult>>;
  
  // 拖拽处理
  processDragDropFiles: (filePaths: string[]) => Promise<ApiResponse<FileSelectResult>>;
  
  // 上传策略分析
  analyzeUploadStrategy: (files: FileInfo[]) => Promise<ApiResponse<UploadStrategyAnalysis>>;
  
  // 智能上传
  smartUpload: (request: SmartUploadRequest) => Promise<ApiResponse<SmartUploadResponse>>;
  
  // 打包相关
  shouldPack: (fileCount: number) => Promise<ApiResponse<boolean>>;
  createPackingTask: (files: FileInfo[], options?: PackingOptions) => Promise<ApiResponse<string>>;
  startPacking: (taskId: string, options?: PackingOptions) => Promise<ApiResponse>;
  cancelPacking: (taskId: string) => Promise<ApiResponse>;
  getPackingTask: (taskId: string) => Promise<ApiResponse<PackingTask | null>>;
  getAllPackingTasks: () => Promise<ApiResponse<PackingTask[]>>;
  clearCompletedPackingTasks: () => Promise<ApiResponse>;
  
  // 上传后处理
  uploadAfterPacking: (packingTaskId: string, metadata?: Record<string, string>) => Promise<ApiResponse<{ taskIds: string[] }>>;
}

/**
 * 新上传架构的事件监听器接口
 */
export interface NewUploadEventListeners {
  // 文件选择事件
  onFileSelectEvent: (callback: (event: FileSelectEvent) => void) => void;
  
  // 打包事件
  onPackingTaskCreated: (callback: (taskId: string, task: PackingTask) => void) => void;
  onPackingTaskProgress: (callback: (event: PackingProgressEvent) => void) => void;
  onPackingTaskStatusChanged: (callback: (event: PackingStatusEvent) => void) => void;
  onPackingTaskCompleted: (callback: (taskId: string, task: PackingTask) => void) => void;
  onPackingTaskFailed: (callback: (taskId: string, error: any) => void) => void;
  onPackingTaskCancelled: (callback: (taskId: string) => void) => void;
  
  // 拖拽事件
  onDragDropFiles: (callback: (data: DragDropEventData) => void) => void;
  
  // 移除监听器
  removeAllListeners: (channel: string) => void;
}

// ==================== 工具函数类型 ====================

/**
 * 文件类型检测函数
 */
export type FileTypeDetector = (filePath: string) => string;

/**
 * 文件大小格式化函数
 */
export type FileSizeFormatter = (bytes: number) => string;

/**
 * 上传时间估算函数
 */
export type UploadTimeEstimator = (totalSize: number, strategy: UploadStrategy) => number;

// ==================== 配置相关类型 ====================

/**
 * 新上传架构配置
 */
export interface NewUploadConfig {
  // 文件选择配置
  fileSelect: {
    defaultFilters: Electron.FileFilter[];
    maxFileSize: number;
    maxFileCount: number;
    allowedExtensions: string[];
    blockedExtensions: string[];
  };
  
  // 打包配置
  packing: {
    threshold: number;
    compressionLevel: number;
    outputDir: string;
    tempDir: string;
    maxPackSize: number;
  };
  
  // 上传策略配置
  strategy: {
    individualThreshold: number;
    batchThreshold: number;
    smartPackThreshold: number;
    autoSelectStrategy: boolean;
  };
  
  // 性能配置
  performance: {
    maxConcurrentPacks: number;
    maxConcurrentUploads: number;
    progressUpdateInterval: number;
    memoryLimit: number;
  };
}

/**
 * 默认配置
 */
export const DEFAULT_NEW_UPLOAD_CONFIG: NewUploadConfig = {
  fileSelect: {
    defaultFilters: [
      { name: "所有文件", extensions: ["*"] },
      { name: "图片", extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"] },
      { name: "视频", extensions: ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"] },
      { name: "文档", extensions: ["pdf", "doc", "docx", "txt", "rtf", "md"] },
    ],
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB
    maxFileCount: 10000,
    allowedExtensions: [],
    blockedExtensions: [".exe", ".bat", ".cmd", ".scr"],
  },
  
  packing: {
    threshold: 50,
    compressionLevel: 1,
    outputDir: "",
    tempDir: "",
    maxPackSize: 5 * 1024 * 1024 * 1024, // 5GB
  },
  
  strategy: {
    individualThreshold: 10,
    batchThreshold: 50,
    smartPackThreshold: 50,
    autoSelectStrategy: true,
  },
  
  performance: {
    maxConcurrentPacks: 2,
    maxConcurrentUploads: 3,
    progressUpdateInterval: 500,
    memoryLimit: 1024 * 1024 * 1024, // 1GB
  },
};

// ==================== 导出所有类型 ====================

export type {
  FileInfo,
  FileSelectOptions,
  FileSelectResult,
  PackingTask,
  PackingOptions,
  PackingStatus,
  ApiResponse,
};
