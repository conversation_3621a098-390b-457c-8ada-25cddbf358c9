import { dialog } from "electron";
import { promises as fs } from "fs";
import { join, basename, extname, relative } from "path";
import { ApiResponse } from "./types";

/**
 * 文件信息接口
 */
export interface FileInfo {
  path: string;           // 文件绝对路径
  name: string;           // 文件名
  size: number;           // 文件大小
  type: string;           // MIME类型
  relativePath?: string;  // 相对路径（用于文件夹上传）
  isDirectory: boolean;   // 是否为目录
  lastModified: number;   // 最后修改时间
}

/**
 * 文件选择选项
 */
export interface FileSelectOptions {
  title?: string;
  buttonLabel?: string;
  filters?: Electron.FileFilter[];
  properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles'>;
  defaultPath?: string;
}

/**
 * 文件选择结果
 */
export interface FileSelectResult {
  success: boolean;
  files: FileInfo[];
  totalSize: number;
  totalCount: number;
  error?: string;
}

/**
 * Electron 文件选择器
 * 提供统一的文件和文件夹选择功能
 */
export class ElectronFileSelector {
  private static instance: ElectronFileSelector;

  public static getInstance(): ElectronFileSelector {
    if (!ElectronFileSelector.instance) {
      ElectronFileSelector.instance = new ElectronFileSelector();
    }
    return ElectronFileSelector.instance;
  }

  /**
   * 选择文件
   */
  async selectFiles(options: FileSelectOptions = {}): Promise<FileSelectResult> {
    try {
      const defaultOptions: FileSelectOptions = {
        title: "选择要上传的文件",
        buttonLabel: "选择",
        properties: ["openFile", "multiSelections"],
        filters: [
          { name: "所有文件", extensions: ["*"] },
          { name: "图片", extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"] },
          { name: "视频", extensions: ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"] },
          { name: "音频", extensions: ["mp3", "wav", "aac", "ogg", "flac", "m4a"] },
          { name: "文档", extensions: ["pdf", "doc", "docx", "txt", "rtf", "md"] },
          { name: "压缩包", extensions: ["zip", "rar", "7z", "tar", "gz"] },
        ],
      };

      const mergedOptions = { ...defaultOptions, ...options };
      
      const result = await dialog.showOpenDialog({
        title: mergedOptions.title,
        buttonLabel: mergedOptions.buttonLabel,
        properties: mergedOptions.properties,
        filters: mergedOptions.filters,
        defaultPath: mergedOptions.defaultPath,
      });

      if (result.canceled || result.filePaths.length === 0) {
        return {
          success: false,
          files: [],
          totalSize: 0,
          totalCount: 0,
          error: "用户取消了文件选择",
        };
      }

      const files = await this.processFilePaths(result.filePaths);
      return {
        success: true,
        files,
        totalSize: files.reduce((sum, file) => sum + file.size, 0),
        totalCount: files.length,
      };
    } catch (error) {
      return {
        success: false,
        files: [],
        totalSize: 0,
        totalCount: 0,
        error: `文件选择失败: ${error}`,
      };
    }
  }

  /**
   * 选择文件夹
   */
  async selectDirectories(options: FileSelectOptions = {}): Promise<FileSelectResult> {
    try {
      const defaultOptions: FileSelectOptions = {
        title: "选择要上传的文件夹",
        buttonLabel: "选择",
        properties: ["openDirectory", "multiSelections"],
      };

      const mergedOptions = { ...defaultOptions, ...options };
      
      const result = await dialog.showOpenDialog({
        title: mergedOptions.title,
        buttonLabel: mergedOptions.buttonLabel,
        properties: mergedOptions.properties,
        defaultPath: mergedOptions.defaultPath,
      });

      if (result.canceled || result.filePaths.length === 0) {
        return {
          success: false,
          files: [],
          totalSize: 0,
          totalCount: 0,
          error: "用户取消了文件夹选择",
        };
      }

      const files = await this.processDirectoryPaths(result.filePaths);
      return {
        success: true,
        files,
        totalSize: files.reduce((sum, file) => sum + file.size, 0),
        totalCount: files.length,
      };
    } catch (error) {
      return {
        success: false,
        files: [],
        totalSize: 0,
        totalCount: 0,
        error: `文件夹选择失败: ${error}`,
      };
    }
  }

  /**
   * 处理文件路径列表
   */
  async processFilePaths(filePaths: string[]): Promise<FileInfo[]> {
    const files: FileInfo[] = [];
    
    for (const filePath of filePaths) {
      try {
        const stats = await fs.stat(filePath);
        
        if (stats.isFile()) {
          const fileInfo = await this.createFileInfo(filePath, stats);
          files.push(fileInfo);
        } else if (stats.isDirectory()) {
          // 如果是目录，递归处理
          const dirFiles = await this.traverseDirectory(filePath);
          files.push(...dirFiles);
        }
      } catch (error) {
        console.warn(`无法处理文件路径 ${filePath}:`, error);
      }
    }
    
    return files;
  }

  /**
   * 处理文件夹路径列表
   */
  async processDirectoryPaths(directoryPaths: string[]): Promise<FileInfo[]> {
    const files: FileInfo[] = [];
    
    for (const dirPath of directoryPaths) {
      try {
        const dirFiles = await this.traverseDirectory(dirPath);
        files.push(...dirFiles);
      } catch (error) {
        console.warn(`无法处理文件夹路径 ${dirPath}:`, error);
      }
    }
    
    return files;
  }

  /**
   * 递归遍历目录
   */
  private async traverseDirectory(dirPath: string, basePath?: string): Promise<FileInfo[]> {
    const files: FileInfo[] = [];
    const currentBasePath = basePath || dirPath;
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(dirPath, entry.name);
        
        if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          const fileInfo = await this.createFileInfo(fullPath, stats, currentBasePath);
          files.push(fileInfo);
        } else if (entry.isDirectory()) {
          // 递归处理子目录
          const subFiles = await this.traverseDirectory(fullPath, currentBasePath);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      console.warn(`无法遍历目录 ${dirPath}:`, error);
    }
    
    return files;
  }

  /**
   * 创建文件信息对象
   */
  private async createFileInfo(filePath: string, stats: any, basePath?: string): Promise<FileInfo> {
    const fileName = basename(filePath);
    const ext = extname(filePath).toLowerCase();
    const mimeType = this.getMimeType(ext);
    
    return {
      path: filePath,
      name: fileName,
      size: stats.size,
      type: mimeType,
      relativePath: basePath ? relative(basePath, filePath) : undefined,
      isDirectory: false,
      lastModified: stats.mtime.getTime(),
    };
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      // 图片
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      
      // 视频
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.wmv': 'video/x-ms-wmv',
      '.flv': 'video/x-flv',
      '.webm': 'video/webm',
      '.mkv': 'video/x-matroska',
      
      // 音频
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.aac': 'audio/aac',
      '.ogg': 'audio/ogg',
      '.flac': 'audio/flac',
      '.m4a': 'audio/mp4',
      
      // 文档
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.txt': 'text/plain',
      '.rtf': 'application/rtf',
      '.md': 'text/markdown',
      
      // 压缩包
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.7z': 'application/x-7z-compressed',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip',
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }
}
