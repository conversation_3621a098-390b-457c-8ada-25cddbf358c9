import { BrowserWindow, ipcMain } from "electron";
import { ElectronFileSelector } from "./fileSelector";
import type { DragDropEventData, DragDropFileInfo } from "./newUploadTypes";
import type { ApiResponse } from "./types";

/**
 * Electron 拖拽处理器
 * 处理文件和文件夹的拖拽操作
 */
export class DragDropHandler {
  private static instance: DragDropHandler;
  private fileSelector: ElectronFileSelector;
  private mainWindow: BrowserWindow | null = null;

  private constructor() {
    this.fileSelector = ElectronFileSelector.getInstance();
  }

  public static getInstance(): DragDropHandler {
    if (!DragDropHandler.instance) {
      DragDropHandler.instance = new DragDropHandler();
    }
    return DragDropHandler.instance;
  }

  /**
   * 初始化拖拽处理器
   */
  public initialize(mainWindow: BrowserWindow): void {
    this.mainWindow = mainWindow;
    this.setupDragDropHandlers();
    this.registerIpcHandlers();
  }

  /**
   * 设置拖拽处理器
   */
  private setupDragDropHandlers(): void {
    if (!this.mainWindow) return;

    // 监听文件拖拽事件
    this.mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
      // 阻止拖拽文件导致的页面导航
      const parsedUrl = new URL(navigationUrl);
      if (parsedUrl.protocol === "file:") {
        event.preventDefault();
      }
    });

    // 设置拖拽区域
    this.mainWindow.webContents.on("dom-ready", () => {
      // 注入拖拽处理脚本
      this.mainWindow?.webContents.executeJavaScript(`
        // 移除之前的监听器（如果存在）
        if (window.__dragDropHandlersAdded) {
          return;
        }
        window.__dragDropHandlersAdded = true;

        // 拖拽状态管理
        let dragCounter = 0;

        // 阻止默认的拖拽行为
        const handleDragOver = (e) => {
          e.preventDefault();
          e.stopPropagation();
          e.dataTransfer.dropEffect = 'copy';
        };

        const handleDragEnter = (e) => {
          e.preventDefault();
          e.stopPropagation();
          dragCounter++;

          // 添加拖拽样式
          document.body.classList.add('drag-over');
        };

        const handleDragLeave = (e) => {
          e.preventDefault();
          e.stopPropagation();
          dragCounter--;

          // 移除拖拽样式
          if (dragCounter === 0) {
            document.body.classList.remove('drag-over');
          }
        };

        const handleDrop = (e) => {
          e.preventDefault();
          e.stopPropagation();
          dragCounter = 0;

          // 移除拖拽样式
          document.body.classList.remove('drag-over');

          // 获取拖拽的文件路径
          const files = Array.from(e.dataTransfer.files);
          const filePaths = files.map(file => file.path).filter(path => path);

          if (filePaths.length > 0) {
            // 通过IPC发送到主进程处理
            if (window.electronAPI?.tus?.processDragDropFiles) {
              window.electronAPI.tus.processDragDropFiles(filePaths)
                .then(result => {
                  if (result.success) {
                    console.log('拖拽文件处理成功:', result.data);
                  } else {
                    console.error('拖拽文件处理失败:', result.error);
                  }
                })
                .catch(error => {
                  console.error('拖拽文件处理异常:', error);
                });
            }
          }
        };

        // 添加事件监听器
        document.addEventListener('dragover', handleDragOver);
        document.addEventListener('dragenter', handleDragEnter);
        document.addEventListener('dragleave', handleDragLeave);
        document.addEventListener('drop', handleDrop);

        // 添加拖拽样式
        const style = document.createElement('style');
        style.textContent = \`
          body.drag-over {
            position: relative;
          }
          body.drag-over::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(59, 130, 246, 0.1);
            border: 2px dashed #3b82f6;
            z-index: 9999;
            pointer-events: none;
          }
          body.drag-over::after {
            content: '拖拽文件到此处上传';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            z-index: 10000;
            pointer-events: none;
          }
        \`;
        document.head.appendChild(style);
      `);
    });
  }

  /**
   * 注册IPC处理器
   */
  private registerIpcHandlers(): void {
    // 处理拖拽文件
    ipcMain.handle("drag-drop-process-files", async (_event, filePaths: string[]): Promise<ApiResponse<DragDropEventData>> => {
      try {
        const result = await this.processDragDropFiles(filePaths);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    });
  }

  /**
   * 处理拖拽的文件路径
   */
  public async processDragDropFiles(filePaths: string[]): Promise<DragDropEventData> {
    try {
      // 使用文件选择器处理文件路径
      const files = await this.fileSelector.processFilePaths(filePaths);

      // 转换为拖拽文件信息格式
      const dragDropFiles: DragDropFileInfo[] = files.map((file) => ({
        path: file.path,
        name: file.name,
        size: file.size,
        type: file.type,
        isDirectory: file.isDirectory,
      }));

      // 统计信息
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const totalCount = files.length;
      const hasDirectories = files.some((file) => file.isDirectory);

      const eventData: DragDropEventData = {
        files: dragDropFiles,
        totalSize,
        totalCount,
        hasDirectories,
      };

      // 发送事件到渲染进程
      this.emitDragDropEvent(eventData);

      return eventData;
    } catch (error) {
      throw new Error(`处理拖拽文件失败: ${error}`);
    }
  }

  /**
   * 发送拖拽事件到渲染进程
   */
  private emitDragDropEvent(data: DragDropEventData): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send("drag-drop-files", data);
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 移除IPC处理器
    ipcMain.removeHandler("drag-drop-process-files");
    this.mainWindow = null;
  }
}

/**
 * 注册拖拽相关的IPC处理器
 */
export function registerDragDropIpcHandlers(): void {
  const dragDropHandler = DragDropHandler.getInstance();

  // 处理拖拽文件（兼容旧接口）
  ipcMain.handle("tus-process-drag-drop", async (_event, filePaths: string[]): Promise<ApiResponse> => {
    try {
      const result = await dragDropHandler.processDragDropFiles(filePaths);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });
}

/**
 * 移除拖拽相关的IPC处理器
 */
export function unregisterDragDropIpcHandlers(): void {
  const handlers = ["drag-drop-process-files", "tus-process-drag-drop"];

  handlers.forEach((handler) => {
    ipcMain.removeHandler(handler);
  });
}

/**
 * 初始化拖拽处理器（便捷函数）
 */
export function initializeDragDropHandler(mainWindow: BrowserWindow): DragDropHandler {
  const handler = DragDropHandler.getInstance();
  handler.initialize(mainWindow);
  registerDragDropIpcHandlers();
  return handler;
}
