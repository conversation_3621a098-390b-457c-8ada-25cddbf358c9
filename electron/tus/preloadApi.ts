import { ipc<PERSON>ender<PERSON> } from "electron";
import type { TusUploadConfig, UploadTask, ApiResponse } from "./types";
import type { FileSelectOptions, FileSelectResult } from "./fileSelector";
import type { PackingOptions, PackingTask } from "./smartPacker";
import type { UploadStrategyAnalysis, DragDropEventData } from "./newUploadTypes";

// TUS 上传 API 接口
export interface TusPreloadApi {
  // 创建上传任务
  createUpload: (filePath: string, metadata?: Record<string, string>) => Promise<ApiResponse>;

  // 从 File 对象创建上传任务
  createUploadFromFile: (fileData: { name: string; content: ArrayBuffer; metadata?: Record<string, string> }) => Promise<ApiResponse>;

  // 通过文件选择对话框创建上传任务（推荐用于避免大文件卡死）
  createUploadFromDialog: () => Promise<ApiResponse>;

  // 从文件路径列表创建批量上传任务（推荐用于大文件）
  createUploadsFromPaths: (filePaths: string[], metadata?: Record<string, string>) => Promise<ApiResponse>;

  // 获取文件信息（不读取文件内容）
  getFileInfo: (filePath: string) => Promise<ApiResponse>;

  // 开始上传
  startUpload: (taskId: string) => Promise<ApiResponse>;

  // 暂停上传
  pauseUpload: (taskId: string) => Promise<ApiResponse>;

  // 恢复上传
  resumeUpload: (taskId: string) => Promise<ApiResponse>;

  // 取消上传
  cancelUpload: (taskId: string) => Promise<ApiResponse>;

  // 重试上传
  retryUpload: (taskId: string) => Promise<ApiResponse>;

  // 删除任务
  deleteTask: (taskId: string) => Promise<ApiResponse>;

  // 获取所有任务
  getAllTasks: () => Promise<ApiResponse>;

  // 获取指定任务
  getTask: (taskId: string) => Promise<ApiResponse>;

  // 获取活跃任务
  getActiveTasks: () => Promise<ApiResponse>;

  // 获取任务的上传URL
  getTaskUploadUrl: (taskId: string) => Promise<ApiResponse>;

  // 更新配置
  updateConfig: (config: Partial<TusUploadConfig>) => Promise<ApiResponse>;

  // 清理已完成的任务
  clearCompletedTasks: () => Promise<ApiResponse>;

  // 清空所有任务
  clearAllTasks: () => Promise<ApiResponse>;

  // ==================== 新增：文件选择相关方法 ====================

  // 选择文件
  selectFiles: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;

  // 选择文件夹
  selectDirectories: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;

  // 处理文件路径列表
  processFilePaths: (filePaths: string[]) => Promise<ApiResponse<FileSelectResult>>;

  // ==================== 新增：智能打包相关方法 ====================

  // 判断是否需要打包
  shouldPack: (fileCount: number) => Promise<ApiResponse<boolean>>;

  // 创建打包任务
  createPackingTask: (files: any[], options?: PackingOptions) => Promise<ApiResponse<string>>;

  // 开始打包
  startPacking: (taskId: string, options?: PackingOptions) => Promise<ApiResponse>;

  // 取消打包
  cancelPacking: (taskId: string) => Promise<ApiResponse>;

  // 获取打包任务
  getPackingTask: (taskId: string) => Promise<ApiResponse<PackingTask | null>>;

  // 获取所有打包任务
  getAllPackingTasks: () => Promise<ApiResponse<PackingTask[]>>;

  // 清理已完成的打包任务
  clearCompletedPackingTasks: () => Promise<ApiResponse>;

  // ==================== 新增：智能上传方法 ====================

  // 智能上传：自动判断是否需要打包
  smartUpload: (files: any[], metadata?: Record<string, string>) => Promise<ApiResponse<{ taskIds: string[]; packingTaskId?: string }>>;

  // 打包完成后创建上传任务
  uploadAfterPacking: (packingTaskId: string, metadata?: Record<string, string>) => Promise<ApiResponse<{ taskIds: string[] }>>;

  // ==================== 新增：拖拽处理相关方法 ====================

  // 处理拖拽文件
  processDragDropFiles: (filePaths: string[]) => Promise<ApiResponse<DragDropEventData>>;

  // ==================== 新增：上传策略分析相关方法 ====================

  // 分析上传策略
  analyzeUploadStrategy: (files: any[]) => Promise<ApiResponse<UploadStrategyAnalysis>>;

  // 获取策略建议
  getStrategyRecommendation: (analysis: UploadStrategyAnalysis) => Promise<ApiResponse<string>>;

  // 比较上传策略
  compareStrategies: (files: any[], strategy1: string, strategy2: string) => Promise<ApiResponse<any>>;
}

// TUS 事件监听器接口
export interface TusEventListeners {
  // TUS 上传事件监听器
  onUploadTaskCreated: (callback: (taskId: string, task: UploadTask) => void) => void;

  onUploadTaskProgress: (callback: (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void) => void;

  onUploadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;

  onUploadTaskCompleted: (callback: (taskId: string) => void) => void;

  onUploadTaskError: (callback: (taskId: string, error: string) => void) => void;

  // 移除事件监听器
  removeAllListeners: (channel: string) => void;
}

// 简化的 TUS Preload API 接口（整合了 API 和事件监听器）
export interface SimplifiedTusPreloadApi extends TusPreloadApi {
  // 事件监听器
  onUploadTaskCreated: (callback: (taskId: string, task: UploadTask) => void) => void;
  onUploadTaskProgress: (callback: (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void) => void;
  onUploadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
  onUploadTaskCompleted: (callback: (taskId: string) => void) => void;
  onUploadTaskError: (callback: (taskId: string, error: string) => void) => void;
  removeAllListeners: (channel: string) => void;
}

/**
 * 创建 TUS 上传 API
 */
export function createTusApi(): TusPreloadApi {
  return {
    // 创建上传任务
    createUpload: (filePath: string, metadata?: Record<string, string>) => ipcRenderer.invoke("tus-create-upload", filePath, metadata),

    // 从 File 对象创建上传任务
    createUploadFromFile: (fileData: { name: string; content: ArrayBuffer; metadata?: Record<string, string> }) => ipcRenderer.invoke("tus-create-upload-from-file", fileData),

    // 通过文件选择对话框创建上传任务（推荐用于避免大文件卡死）
    createUploadFromDialog: () => ipcRenderer.invoke("tus-create-upload-from-dialog"),

    // 从文件路径列表创建批量上传任务（推荐用于大文件）
    createUploadsFromPaths: (filePaths: string[], metadata?: Record<string, string>) => ipcRenderer.invoke("tus-create-uploads-from-paths", filePaths, metadata),

    // 获取文件信息（不读取文件内容）
    getFileInfo: (filePath: string) => ipcRenderer.invoke("tus-get-file-info", filePath),

    // 开始上传
    startUpload: (taskId: string) => ipcRenderer.invoke("tus-start-upload", taskId),

    // 暂停上传
    pauseUpload: (taskId: string) => ipcRenderer.invoke("tus-pause-upload", taskId),

    // 恢复上传
    resumeUpload: (taskId: string) => ipcRenderer.invoke("tus-resume-upload", taskId),

    // 取消上传
    cancelUpload: (taskId: string) => ipcRenderer.invoke("tus-cancel-upload", taskId),

    // 重试上传
    retryUpload: (taskId: string) => ipcRenderer.invoke("tus-retry-upload", taskId),

    // 删除任务
    deleteTask: (taskId: string) => ipcRenderer.invoke("tus-delete-task", taskId),

    // 获取所有任务
    getAllTasks: () => ipcRenderer.invoke("tus-get-all-tasks"),

    // 获取指定任务
    getTask: (taskId: string) => ipcRenderer.invoke("tus-get-task", taskId),

    // 获取活跃任务
    getActiveTasks: () => ipcRenderer.invoke("tus-get-active-tasks"),

    // 获取任务的上传URL
    getTaskUploadUrl: (taskId: string) => ipcRenderer.invoke("tus-get-task-upload-url", taskId),

    // 更新配置
    updateConfig: (config: Partial<TusUploadConfig>) => ipcRenderer.invoke("tus-update-config", config),

    // 清理已完成的任务
    clearCompletedTasks: () => ipcRenderer.invoke("tus-clear-completed-tasks"),

    // 清空所有任务
    clearAllTasks: () => ipcRenderer.invoke("tus-clear-all-tasks"),

    // ==================== 新增：文件选择相关方法 ====================

    // 选择文件
    selectFiles: (options?: FileSelectOptions) => ipcRenderer.invoke("file-select-files", options),

    // 选择文件夹
    selectDirectories: (options?: FileSelectOptions) => ipcRenderer.invoke("file-select-directories", options),

    // 处理文件路径列表
    processFilePaths: (filePaths: string[]) => ipcRenderer.invoke("file-process-paths", filePaths),

    // ==================== 新增：智能打包相关方法 ====================

    // 判断是否需要打包
    shouldPack: (fileCount: number) => ipcRenderer.invoke("pack-should-pack", fileCount),

    // 创建打包任务
    createPackingTask: (files: any[], options?: PackingOptions) => ipcRenderer.invoke("pack-create-task", files, options),

    // 开始打包
    startPacking: (taskId: string, options?: PackingOptions) => ipcRenderer.invoke("pack-start", taskId, options),

    // 取消打包
    cancelPacking: (taskId: string) => ipcRenderer.invoke("pack-cancel", taskId),

    // 获取打包任务
    getPackingTask: (taskId: string) => ipcRenderer.invoke("pack-get-task", taskId),

    // 获取所有打包任务
    getAllPackingTasks: () => ipcRenderer.invoke("pack-get-all-tasks"),

    // 清理已完成的打包任务
    clearCompletedPackingTasks: () => ipcRenderer.invoke("pack-clear-completed"),

    // ==================== 新增：智能上传方法 ====================

    // 智能上传：自动判断是否需要打包
    smartUpload: (files: any[], metadata?: Record<string, string>) => ipcRenderer.invoke("upload-smart", files, metadata),

    // 打包完成后创建上传任务
    uploadAfterPacking: (packingTaskId: string, metadata?: Record<string, string>) => ipcRenderer.invoke("upload-after-packing", packingTaskId, metadata),

    // ==================== 新增：拖拽处理相关方法 ====================

    // 处理拖拽文件
    processDragDropFiles: (filePaths: string[]) => ipcRenderer.invoke("drag-drop-process-files", filePaths),

    // ==================== 新增：上传策略分析相关方法 ====================

    // 分析上传策略
    analyzeUploadStrategy: (files: any[]) => ipcRenderer.invoke("upload-analyze-strategy", files),

    // 获取策略建议
    getStrategyRecommendation: (analysis: UploadStrategyAnalysis) => ipcRenderer.invoke("upload-get-strategy-recommendation", analysis),

    // 比较上传策略
    compareStrategies: (files: any[], strategy1: string, strategy2: string) => ipcRenderer.invoke("upload-compare-strategies", files, strategy1, strategy2),
  };
}

/**
 * 创建 TUS 事件监听器
 */
export function createTusEventListeners(): TusEventListeners {
  return {
    // TUS 上传事件监听器
    onUploadTaskCreated: (callback: (taskId: string, task: UploadTask) => void) => {
      ipcRenderer.on("upload-task-created", (_event, taskId, task) => callback(taskId, task));
    },

    onUploadTaskProgress: (callback: (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void) => {
      ipcRenderer.on("upload-task-progress", (_event, taskId, progress, bytesUploaded, bytesTotal) => callback(taskId, progress, bytesUploaded, bytesTotal));
    },

    onUploadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => {
      ipcRenderer.on("upload-task-status-changed", (_event, taskId, status, error) => callback(taskId, status, error));
    },

    onUploadTaskCompleted: (callback: (taskId: string) => void) => {
      ipcRenderer.on("upload-task-completed", (_event, taskId) => callback(taskId));
    },

    onUploadTaskError: (callback: (taskId: string, error: string) => void) => {
      ipcRenderer.on("upload-task-error", (_event, taskId, error) => callback(taskId, error));
    },

    // 移除事件监听器
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel);
    },
  };
}

/**
 * 创建简化的 TUS Preload API
 * 将 API 调用和事件监听器整合在一个对象中，简化 preload.ts 的使用
 */
export function createSimplifiedTusPreloadApi(): SimplifiedTusPreloadApi {
  const api = createTusApi();
  const eventListeners = createTusEventListeners();

  return {
    // API 方法
    ...api,

    // 事件监听器
    ...eventListeners,
  };
}
