# Electron 原生文件上传架构

## 概述

本文档描述了新的基于 Electron 原生 API 的文件上传架构，该架构完全替代了之前基于 Web File API 的实现。

## 架构特点

### 1. 完全原生化
- **文件选择**：使用 `dialog.showOpenDialog` 替代 `input[type="file"]`
- **拖拽处理**：使用 Electron 原生拖拽事件替代 Web 拖拽 API
- **文件处理**：在主进程中处理文件路径，避免大文件内容通过 IPC 传输

### 2. 智能打包策略
- **自动判断**：文件数量 > 50 时自动使用 7z 压缩
- **快速压缩**：使用 `-mx=1` 参数实现最快压缩速度
- **进度反馈**：实时显示压缩进度

### 3. 统一的 IPC 接口
- **文件选择**：`file-select-files`, `file-select-directories`
- **拖拽处理**：`drag-drop-process-files`
- **智能上传**：`upload-smart`
- **策略分析**：`upload-analyze-strategy`

## 核心组件

### 1. ElectronFileSelector
负责文件和文件夹的选择，支持：
- 单文件/多文件选择
- 文件夹递归遍历
- 文件类型过滤
- 大小限制检查

### 2. SmartPacker
智能打包器，提供：
- 自动打包判断（文件数量阈值）
- 7z 快速压缩
- 压缩进度监控
- 错误处理和降级策略

### 3. DragDropHandler
拖拽处理器，实现：
- 全局拖拽监听
- 文件路径提取
- 拖拽样式管理
- 与文件选择器的集成

### 4. UploadStrategyAnalyzer
上传策略分析器，提供：
- 智能策略推荐
- 上传时间估算
- 策略比较分析
- 用户友好的建议文本

## 前端适配

### 1. useElectronFileUpload
新的 Composable，替代 `useFileUpload`：
```typescript
const {
  files,
  selectFiles,
  selectDirectories,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  removeFile,
  clearFiles
} = useElectronFileUpload(options)
```

### 2. useElectronTusUpload
新的上传管理 Composable：
```typescript
const {
  smartUploadFiles,
  uploadFromDialog,
  pauseUpload,
  resumeUpload,
  cancelUpload
} = useElectronTusUpload()
```

### 3. 组件兼容性
- **FileUploadArea.vue**：完全重构，使用新的 Electron API
- **FileItem.vue**：保持兼容，通过适配层支持新的文件格式
- **UploadDialog.vue**：更新为使用新的上传逻辑

## 数据流

### 1. 文件选择流程
```
用户操作 → Electron Dialog → 主进程文件遍历 → IPC 传递路径 → 前端显示
```

### 2. 智能上传流程
```
文件列表 → 策略分析 → 打包判断 → 压缩/直传 → TUS 上传 → 进度反馈
```

### 3. 拖拽上传流程
```
拖拽事件 → 主进程处理 → 文件路径提取 → 与选择流程合并
```

## 性能优化

### 1. 内存优化
- 不再通过 IPC 传输文件内容
- 主进程直接处理文件路径
- 减少临时文件创建

### 2. 网络优化
- 大量文件自动压缩
- 智能上传策略选择
- 并发上传控制

### 3. 用户体验优化
- 实时进度反馈
- 智能策略建议
- 错误处理和重试机制

## 向后兼容

### 1. API 兼容
- 保持现有的 TUS API 接口
- 支持旧的上传任务格式
- 兼容现有的任务管理功能

### 2. UI 兼容
- 上传弹窗界面保持不变
- 文件列表显示格式兼容
- 进度显示和状态管理一致

## 配置选项

### 1. 文件选择配置
```typescript
interface ElectronFileSelectOptions {
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxFiles?: number
  allowDirectories?: boolean
}
```

### 2. 打包配置
```typescript
interface PackingOptions {
  compressionLevel?: number  // 0-9，默认 1
  outputDir?: string        // 输出目录
  outputName?: string       // 输出文件名
  password?: string         // 压缩密码
}
```

## 错误处理

### 1. 文件选择错误
- 文件不存在或无权限
- 文件大小超限
- 文件数量超限

### 2. 打包错误
- 7z 工具不可用
- 磁盘空间不足
- 文件被占用

### 3. 上传错误
- 网络连接问题
- 服务器错误
- 认证失败

## 测试建议

### 1. 功能测试
- [ ] 单文件选择和上传
- [ ] 多文件选择和上传
- [ ] 文件夹选择和上传
- [ ] 拖拽文件上传
- [ ] 大量文件自动打包
- [ ] 上传暂停和恢复
- [ ] 错误处理和重试

### 2. 性能测试
- [ ] 大文件上传性能
- [ ] 大量小文件打包性能
- [ ] 内存使用情况
- [ ] 网络带宽利用率

### 3. 兼容性测试
- [ ] 不同操作系统兼容性
- [ ] 不同文件类型支持
- [ ] 特殊字符文件名处理
- [ ] 长路径文件处理

## 迁移指南

### 1. 开发者迁移
- 使用新的 Composable 替代旧的
- 更新组件导入路径
- 适配新的文件数据格式

### 2. 用户迁移
- 无需用户操作
- 保持现有使用习惯
- 享受更好的性能和体验

## 总结

新的 Electron 原生文件上传架构提供了：
- 更好的性能和稳定性
- 更智能的上传策略
- 更友好的用户体验
- 更强的扩展性和维护性

该架构完全替代了基于 Web API 的旧实现，为用户提供了更加高效和可靠的文件上传体验。
