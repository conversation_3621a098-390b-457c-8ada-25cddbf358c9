<template>
  <div class="upload-test-simple">
    <h1>简单上传测试</h1>
    <p>测试重构后的上传功能</p>
    
    <div class="test-buttons">
      <button @click="testBasicFunctionality" class="test-btn">测试基础功能</button>
      <button @click="testUploadDialog" class="test-btn">测试上传对话框</button>
    </div>
    
    <div class="status">
      <p>状态: {{ status }}</p>
      <p v-if="error" class="error">错误: {{ error }}</p>
    </div>
    
    <div class="tasks-info">
      <p>任务总数: {{ tusUpload.tasks.size }}</p>
      <p>独立任务数: {{ tusUpload.standaloneTasks.length }}</p>
      <p>活跃任务数: {{ tusUpload.activeTasks.length }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useElectronTusUpload } from '@/components/Upload/composables/useElectronTusUpload'

const tusUpload = useElectronTusUpload()
const status = ref('就绪')
const error = ref('')

const testBasicFunctionality = () => {
  try {
    status.value = '测试基础功能...'
    error.value = ''
    
    // 测试基本属性访问
    console.log('TUS Upload 实例:', tusUpload)
    console.log('任务数量:', tusUpload.tasks.size)
    console.log('是否在 Electron 环境:', tusUpload.isElectron)
    
    status.value = '基础功能测试完成'
  } catch (err) {
    error.value = err instanceof Error ? err.message : String(err)
    status.value = '基础功能测试失败'
  }
}

const testUploadDialog = async () => {
  try {
    status.value = '测试上传对话框...'
    error.value = ''
    
    await tusUpload.uploadFromDialog()
    status.value = '上传对话框测试完成'
  } catch (err) {
    error.value = err instanceof Error ? err.message : String(err)
    status.value = '上传对话框测试失败'
  }
}
</script>

<style scoped>
.upload-test-simple {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.test-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.test-btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background: #0056b3;
}

.status {
  margin: 20px 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.error {
  color: #dc3545;
}

.tasks-info {
  margin: 20px 0;
  padding: 10px;
  background: #e9ecef;
  border-radius: 4px;
}
</style>
