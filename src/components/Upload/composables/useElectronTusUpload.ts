import { ref, computed, onUnmounted } from "vue";
import { toast } from "vue-sonner";
import type { ElectronUploadFile } from "./useElectronFileUpload";
import type { FileInfo, UploadStrategyAnalysis, ApiResponse } from "@/types/electron";

/**
 * Electron TUS上传管理
 */
export function useElectronTusUpload() {
  // 响应式状态
  const isUploading = ref(false);
  const uploadProgress = ref<Record<string, number>>({});
  const uploadTasks = ref<Record<string, any>>({});
  const packingTasks = ref<Record<string, any>>({});
  const currentStrategy = ref<UploadStrategyAnalysis | null>(null);

  // 获取Electron API
  const getElectronAPI = () => {
    const api = (window as any).electronAPI;
    if (!api?.tus) {
      throw new Error("Electron TUS API 不可用");
    }
    return api.tus;
  };

  /**
   * 智能上传文件
   */
  const smartUploadFiles = async (files: ElectronUploadFile[], metadata?: Record<string, string>): Promise<{ success: boolean; taskIds: string[]; packingTaskId?: string }> => {
    if (!files || files.length === 0) {
      toast.error("没有选择文件");
      return { success: false, taskIds: [] };
    }

    try {
      isUploading.value = true;
      const api = getElectronAPI();

      // 转换为FileInfo格式
      const fileInfos: FileInfo[] = files.map((file) => ({
        path: file.path,
        name: file.name,
        size: file.size,
        type: file.type,
        relativePath: file.relativePath,
        isDirectory: file.isDirectory,
        lastModified: file.lastModified,
      }));

      // 分析上传策略
      const strategyResult = await api.analyzeUploadStrategy(fileInfos);
      if (!strategyResult.success) {
        throw new Error(strategyResult.error || "分析上传策略失败");
      }

      currentStrategy.value = strategyResult.data;

      // 显示策略建议
      const recommendationResult = await api.getStrategyRecommendation(strategyResult.data);
      if (recommendationResult.success) {
        toast.info(recommendationResult.data);
      }

      // 执行智能上传
      const uploadResult = await api.smartUpload(fileInfos, metadata);
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || "智能上传失败");
      }

      const { taskIds, packingTaskId } = uploadResult.data;

      // 如果需要打包，监听打包完成事件
      if (packingTaskId) {
        toast.info("文件数量较多，正在压缩打包...");
        await monitorPackingTask(packingTaskId, metadata);
      } else {
        // 直接上传，等待任务创建确认后启动
        await waitForTasksCreation(taskIds);

        // 启动所有任务
        for (const taskId of taskIds) {
          await api.startUpload(taskId);
        }
        toast.success(`已开始上传 ${taskIds.length} 个文件`);
      }

      return { success: true, taskIds, packingTaskId };
    } catch (error) {
      console.error("智能上传失败:", error);
      toast.error(`上传失败: ${error instanceof Error ? error.message : String(error)}`);
      return { success: false, taskIds: [] };
    } finally {
      isUploading.value = false;
    }
  };

  /**
   * 等待任务创建确认
   */
  const waitForTasksCreation = async (taskIds: string[], timeout: number = 10000): Promise<void> => {
    const startTime = Date.now();
    const checkInterval = 500; // 每500ms检查一次

    console.log(`等待 ${taskIds.length} 个任务创建确认...`);

    while (Date.now() - startTime < timeout) {
      try {
        // 检查所有任务是否都已创建
        const api = getElectronAPI();
        const response = await api.tus.getAllTasks();

        if (response.success && response.tasks) {
          const existingTaskIds = response.tasks.map((task) => task.id);
          const allTasksExist = taskIds.every((taskId) => existingTaskIds.includes(taskId));

          if (allTasksExist) {
            console.log(`所有 ${taskIds.length} 个任务创建确认完成`);
            return;
          }
        }

        // 等待后继续检查
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.warn("检查任务创建状态时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时处理
    console.warn(`任务创建确认超时 (${timeout}ms)，尝试刷新任务列表`);
    try {
      const api = getElectronAPI();
      await api.tus.refreshTasks();
    } catch (error) {
      console.error("刷新任务列表失败:", error);
    }
  };

  /**
   * 监听打包任务
   */
  const monitorPackingTask = async (packingTaskId: string, metadata?: Record<string, string>): Promise<void> => {
    return new Promise((resolve, reject) => {
      const api = getElectronAPI();

      // 监听打包完成事件
      const handlePackingComplete = async (event: any, taskId: string, task: any) => {
        if (taskId === packingTaskId) {
          try {
            toast.success("文件打包完成，开始上传压缩包");

            // 创建上传任务
            const uploadResult = await api.uploadAfterPacking(packingTaskId, metadata);
            if (uploadResult.success) {
              // 启动上传任务
              for (const uploadTaskId of uploadResult.data.taskIds) {
                await api.startUpload(uploadTaskId);
              }
              toast.success("压缩包上传已开始");
            } else {
              throw new Error(uploadResult.error || "创建上传任务失败");
            }

            resolve();
          } catch (error) {
            reject(error);
          } finally {
            // 清理事件监听器
            window.removeEventListener("packing-task-completed", handlePackingComplete);
            window.removeEventListener("packing-task-failed", handlePackingFailed);
          }
        }
      };

      // 监听打包失败事件
      const handlePackingFailed = (event: any, taskId: string, error: any) => {
        if (taskId === packingTaskId) {
          toast.error(`文件打包失败: ${error}`);
          reject(new Error(`打包失败: ${error}`));

          // 清理事件监听器
          window.removeEventListener("packing-task-completed", handlePackingComplete);
          window.removeEventListener("packing-task-failed", handlePackingFailed);
        }
      };

      // 添加事件监听器
      window.addEventListener("packing-task-completed", handlePackingComplete);
      window.addEventListener("packing-task-failed", handlePackingFailed);

      // 设置超时
      setTimeout(() => {
        window.removeEventListener("packing-task-completed", handlePackingComplete);
        window.removeEventListener("packing-task-failed", handlePackingFailed);
        reject(new Error("打包任务超时"));
      }, 10 * 60 * 1000); // 10分钟超时
    });
  };

  /**
   * 使用文件选择对话框上传
   */
  const uploadFromDialog = async (): Promise<void> => {
    try {
      isUploading.value = true;
      const api = getElectronAPI();

      const result = await api.createUploadFromDialog();
      if (result.success && result.data?.taskIds) {
        const taskIds = result.data.taskIds;

        // 启动所有任务
        for (const taskId of taskIds) {
          await api.startUpload(taskId);
        }

        toast.success(`已添加 ${taskIds.length} 个文件到上传队列`);
      } else if (result.error && !result.error.includes("用户取消")) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("文件选择上传失败:", error);
      toast.error(`文件选择失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      isUploading.value = false;
    }
  };

  /**
   * 获取所有上传任务
   */
  const getAllTasks = async () => {
    try {
      const api = getElectronAPI();
      const result = await api.getAllTasks();
      if (result.success) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error("获取上传任务失败:", error);
      return [];
    }
  };

  /**
   * 获取所有打包任务
   */
  const getAllPackingTasks = async () => {
    try {
      const api = getElectronAPI();
      const result = await api.getAllPackingTasks();
      if (result.success) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error("获取打包任务失败:", error);
      return [];
    }
  };

  /**
   * 暂停上传任务
   */
  const pauseUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getElectronAPI();
      const result = await api.pauseUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "暂停上传失败");
      }
    } catch (error) {
      console.error("暂停上传失败:", error);
      toast.error(`暂停上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 恢复上传任务
   */
  const resumeUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getElectronAPI();
      const result = await api.resumeUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "恢复上传失败");
      }
    } catch (error) {
      console.error("恢复上传失败:", error);
      toast.error(`恢复上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 取消上传任务
   */
  const cancelUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getElectronAPI();
      const result = await api.cancelUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "取消上传失败");
      }
    } catch (error) {
      console.error("取消上传失败:", error);
      toast.error(`取消上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 重试上传任务
   */
  const retryUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getElectronAPI();
      const result = await api.retryUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "重试上传失败");
      }
    } catch (error) {
      console.error("重试上传失败:", error);
      toast.error(`重试上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 清理已完成的任务
   */
  const clearCompletedTasks = async (): Promise<void> => {
    try {
      const api = getElectronAPI();
      const result = await api.clearCompletedTasks();
      if (!result.success) {
        throw new Error(result.error || "清理任务失败");
      }
      toast.success("已清理完成的任务");
    } catch (error) {
      console.error("清理任务失败:", error);
      toast.error(`清理任务失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // 计算属性
  const hasActiveTasks = computed(() => {
    return Object.keys(uploadTasks.value).length > 0 || Object.keys(packingTasks.value).length > 0;
  });

  // 清理函数
  onUnmounted(() => {
    // 清理事件监听器
    // 这里可以添加清理逻辑
  });

  return {
    // 状态
    isUploading,
    uploadProgress,
    uploadTasks,
    packingTasks,
    currentStrategy,
    hasActiveTasks,

    // 方法
    smartUploadFiles,
    uploadFromDialog,
    getAllTasks,
    getAllPackingTasks,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    clearCompletedTasks,
  };
}
