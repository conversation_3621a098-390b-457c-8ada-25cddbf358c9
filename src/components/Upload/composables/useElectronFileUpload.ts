import { ref, computed, reactive } from 'vue'
import { toast } from 'vue-sonner'
import type { FileInfo } from '@/types/electron'

/**
 * Electron文件上传接口
 */
export interface ElectronUploadFile {
  id: string
  name: string
  size: number
  type: string
  path: string
  relativePath?: string
  isDirectory: boolean
  lastModified: number
}

/**
 * 文件选择选项
 */
export interface ElectronFileSelectOptions {
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxFiles?: number
  allowDirectories?: boolean
}

/**
 * 使用Electron原生文件上传
 */
export function useElectronFileUpload(options: ElectronFileSelectOptions = {}) {
  // 响应式状态
  const files = ref<ElectronUploadFile[]>([])
  const isDragging = ref(false)
  const isSelecting = ref(false)
  const errors = ref<string[]>([])

  // 配置选项
  const config = reactive({
    accept: options.accept || '',
    multiple: options.multiple ?? true,
    maxSize: options.maxSize || 100 * 1024 * 1024, // 100MB
    maxFiles: options.maxFiles || 10,
    allowDirectories: options.allowDirectories ?? true,
  })

  // 计算属性
  const totalSize = computed(() => {
    return files.value.reduce((sum, file) => sum + file.size, 0)
  })

  const canAddMore = computed(() => {
    return files.value.length < config.maxFiles
  })

  const hasFiles = computed(() => {
    return files.value.length > 0
  })

  // 获取Electron API
  const getElectronAPI = () => {
    const api = (window as any).electronAPI
    if (!api?.tus) {
      throw new Error('Electron API 不可用')
    }
    return api.tus
  }

  /**
   * 选择文件
   */
  const selectFiles = async (): Promise<void> => {
    if (!canAddMore.value) {
      toast.error(`最多只能选择 ${config.maxFiles} 个文件`)
      return
    }

    try {
      isSelecting.value = true
      const api = getElectronAPI()
      
      const result = await api.selectFiles({
        title: '选择要上传的文件',
        properties: ['openFile', config.multiple ? 'multiSelections' : undefined].filter(Boolean),
        filters: parseAcceptToFilters(config.accept),
      })

      if (result.success && result.data?.files) {
        await addFilesFromElectron(result.data.files)
      } else if (result.error && !result.error.includes('用户取消')) {
        toast.error(`选择文件失败: ${result.error}`)
      }
    } catch (error) {
      console.error('选择文件失败:', error)
      toast.error('选择文件时发生错误')
    } finally {
      isSelecting.value = false
    }
  }

  /**
   * 选择文件夹
   */
  const selectDirectories = async (): Promise<void> => {
    if (!config.allowDirectories) {
      toast.error('不支持选择文件夹')
      return
    }

    if (!canAddMore.value) {
      toast.error(`最多只能选择 ${config.maxFiles} 个文件`)
      return
    }

    try {
      isSelecting.value = true
      const api = getElectronAPI()
      
      const result = await api.selectDirectories({
        title: '选择要上传的文件夹',
        properties: ['openDirectory', config.multiple ? 'multiSelections' : undefined].filter(Boolean),
      })

      if (result.success && result.data?.files) {
        await addFilesFromElectron(result.data.files)
      } else if (result.error && !result.error.includes('用户取消')) {
        toast.error(`选择文件夹失败: ${result.error}`)
      }
    } catch (error) {
      console.error('选择文件夹失败:', error)
      toast.error('选择文件夹时发生错误')
    } finally {
      isSelecting.value = false
    }
  }

  /**
   * 处理拖拽文件
   */
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy'
    }
    isDragging.value = true
  }

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    isDragging.value = false
  }

  const handleDrop = async (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    isDragging.value = false

    if (!canAddMore.value) {
      toast.error(`最多只能选择 ${config.maxFiles} 个文件`)
      return
    }

    try {
      // 获取拖拽的文件路径
      const files = Array.from(event.dataTransfer?.files || [])
      const filePaths = files.map(file => (file as any).path).filter(path => path)

      if (filePaths.length > 0) {
        const api = getElectronAPI()
        const result = await api.processDragDropFiles(filePaths)

        if (result.success && result.data?.files) {
          await addFilesFromElectron(result.data.files)
        } else {
          toast.error(`处理拖拽文件失败: ${result.error}`)
        }
      }
    } catch (error) {
      console.error('处理拖拽文件失败:', error)
      toast.error('处理拖拽文件时发生错误')
    }
  }

  /**
   * 从Electron文件信息添加文件
   */
  const addFilesFromElectron = async (electronFiles: FileInfo[]): Promise<void> => {
    const newFiles: ElectronUploadFile[] = []
    const newErrors: string[] = []

    for (const electronFile of electronFiles) {
      // 检查文件大小
      if (electronFile.size > config.maxSize) {
        newErrors.push(`文件 ${electronFile.name} 超过大小限制 (${formatFileSize(config.maxSize)})`)
        continue
      }

      // 检查文件数量限制
      if (files.value.length + newFiles.length >= config.maxFiles) {
        newErrors.push(`最多只能选择 ${config.maxFiles} 个文件`)
        break
      }

      // 检查是否已存在
      const exists = files.value.some(f => f.path === electronFile.path)
      if (exists) {
        newErrors.push(`文件 ${electronFile.name} 已存在`)
        continue
      }

      // 创建上传文件对象
      const uploadFile: ElectronUploadFile = {
        id: generateFileId(),
        name: electronFile.name,
        size: electronFile.size,
        type: electronFile.type,
        path: electronFile.path,
        relativePath: electronFile.relativePath,
        isDirectory: electronFile.isDirectory,
        lastModified: electronFile.lastModified,
      }

      newFiles.push(uploadFile)
    }

    // 添加新文件
    files.value.push(...newFiles)

    // 处理错误
    if (newErrors.length > 0) {
      errors.value = newErrors
      newErrors.forEach(error => toast.error(error))
    }

    // 成功提示
    if (newFiles.length > 0) {
      toast.success(`已添加 ${newFiles.length} 个文件`)
    }
  }

  /**
   * 移除文件
   */
  const removeFile = (fileId: string): void => {
    const index = files.value.findIndex(f => f.id === fileId)
    if (index > -1) {
      const removedFile = files.value.splice(index, 1)[0]
      toast.info(`已移除文件: ${removedFile.name}`)
    }
  }

  /**
   * 清空所有文件
   */
  const clearFiles = (): void => {
    const count = files.value.length
    files.value = []
    errors.value = []
    if (count > 0) {
      toast.info(`已清空 ${count} 个文件`)
    }
  }

  /**
   * 解析accept属性为Electron文件过滤器
   */
  const parseAcceptToFilters = (accept: string): Electron.FileFilter[] => {
    if (!accept) {
      return [{ name: '所有文件', extensions: ['*'] }]
    }

    const filters: Electron.FileFilter[] = []
    const extensions = accept.split(',').map(ext => ext.trim().replace('.', ''))
    
    if (extensions.length > 0) {
      filters.push({ name: '选择的文件类型', extensions })
    }
    
    filters.push({ name: '所有文件', extensions: ['*'] })
    return filters
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 生成文件ID
   */
  const generateFileId = (): string => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  return {
    // 状态
    files,
    isDragging,
    isSelecting,
    errors,
    totalSize,
    canAddMore,
    hasFiles,

    // 配置
    config,

    // 方法
    selectFiles,
    selectDirectories,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    removeFile,
    clearFiles,
    formatFileSize,
  }
}
