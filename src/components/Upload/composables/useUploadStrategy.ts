import { useElectronTusUpload } from "@/components/Upload/composables/useElectronTusUpload";

/**
 * 高级上传策略 Composable
 * 提供简化的接口和自动错误处理
 *
 * 使用场景：
 * - 需要简单上传功能，不想处理复杂的回调逻辑
 * - 需要统一的错误处理，由全局任务管理器负责用户提示
 * - 需要便捷的拖拽、文件输入等辅助方法
 */
export function useUploadStrategy() {
  const electronTusUpload = useElectronTusUpload();

  /**
   * 智能上传文件 - 统一入口
   * 自动判断并选择最佳上传方式，使用Electron原生上传
   */
  const uploadFiles = async (
    files: any[], // 兼容新的文件格式
    attributes?: Record<string, string>,
    options?: {
      onFileUploaded?: (file: any, task: any) => void;
      onAllFilesUploaded?: (files: any[], tasks: any[]) => void;
      onUploadError?: (error: string, failedTasks: any[]) => void;
    }
  ): Promise<void> => {
    if (!files || files.length === 0) {
      console.warn("没有选择文件");
      return;
    }

    try {
      console.log(`开始智能上传 ${files.length} 个文件`);

      // 使用增强的元数据
      const metadata = {
        ...attributes,
        uploadTimestamp: new Date().toISOString(),
        uploadSource: "strategy-manager", // 标识来源
      };

      // 使用Electron智能上传
      const result = await electronTusUpload.smartUploadFiles(files, metadata);

      if (result.success) {
        // 调用成功回调
        options?.onAllFilesUploaded?.(files, result.taskIds);
      } else {
        // 调用错误回调
        options?.onUploadError?.("上传失败", []);
    } catch (error) {
      console.error("文件上传失败:", error);
      options?.onUploadError?.(error instanceof Error ? error.message : String(error), []);
      throw error;
    }
  };

  /**
   * 通过文件选择对话框上传
   */
  const uploadFromDialog = async (): Promise<void> => {
    try {
      console.log("通过文件选择对话框上传");

      // 使用Electron文件选择对话框上传
      await electronTusUpload.uploadFromDialog();
    } catch (error) {
      console.error("文件选择上传失败:", error);
      throw error;
    }
  };

  /**
   * 处理拖拽上传 - 简化接口
   */
  const handleDrop = async (files: any[], attributes?: Record<string, string>, options?: Parameters<typeof uploadFiles>[2]): Promise<void> => {
    return uploadFiles(files, { ...attributes, uploadSource: "drag-drop" }, options);
  };

  /**
   * 处理文件输入上传 - 简化接口
   */
  const handleFileInput = async (files: any[], attributes?: Record<string, string>, options?: Parameters<typeof uploadFiles>[2]): Promise<void> => {
    return uploadFiles(files, { ...attributes, uploadSource: "file-input" }, options);
  };

  /**
   * 创建预设的上传选项
   */
  const createUploadOptions = (overrides?: Partial<Parameters<typeof uploadFiles>[2]>) => {
    return {
      ...overrides,
    };
  };

  /**
   * 静默上传（无提示）
   */
  const uploadFilesSilently = async (
    files: any[],
    attributes?: Record<string, string>,
    callbacks?: {
      onFileUploaded?: (file: any, task: any) => void;
      onAllFilesUploaded?: (files: any[], tasks: any[]) => void;
      onUploadError?: (error: string, failedTasks: any[]) => void;
    }
  ): Promise<void> => {
    return uploadFiles(files, attributes, callbacks);
  };

  return {
    // 主要方法
    uploadFiles,
    uploadFromDialog,
    uploadFilesSilently,

    // 便捷方法
    handleDrop,
    handleFileInput,

    // 工具方法
    createUploadOptions,

    // 底层访问（高级用法）
    electronTusUpload,
  };
}

// 导出上传选项类型
export type UploadStrategyOptions = Parameters<ReturnType<typeof useUploadStrategy>["uploadFiles"]>[2];

// 导出便捷类型
export type { UploadCallbacks };
